#!/usr/bin/env python3
"""
测试流程控制修复效果
验证任务指导生成完成后才开始内容生成
"""

import os
import sys
import asyncio
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

def test_flow_control_logic():
    """测试流程控制逻辑"""
    print("🧪 测试流程控制逻辑")
    print("=" * 50)
    
    try:
        from complete_report_generator import CompleteReportGenerator
        
        # 创建生成器
        generator = CompleteReportGenerator(use_async=False)
        
        # 测试节点统计功能
        print("📊 测试节点统计功能...")
        
        # 创建测试框架
        test_framework = {
            "sections": [
                {
                    "title": "第一章：概述",
                    "level": 1,
                    "task_instruction": {
                        "content_requirements": "全面分析",
                        "word_count": "1000字"
                    },
                    "children": [
                        {
                            "title": "1.1 基本概念",
                            "level": 2,
                            "task_instruction": {
                                "content_requirements": "定义分析",
                                "word_count": "500字"
                            },
                            "children": []
                        },
                        {
                            "title": "1.2 发展历程",
                            "level": 2,
                            # 故意缺少task_instruction
                            "children": []
                        }
                    ]
                },
                {
                    "title": "第二章：技术分析",
                    "level": 1,
                    "task_instruction": {
                        "content_requirements": "技术深度分析",
                        "word_count": "1500字"
                    },
                    "children": []
                }
            ]
        }
        
        # 测试节点统计
        total_nodes = generator._count_all_nodes(test_framework["sections"])
        nodes_with_instructions = generator._count_nodes_with_instructions(test_framework["sections"])
        
        print(f"   总节点数: {total_nodes}")
        print(f"   有指导的节点: {nodes_with_instructions}")
        print(f"   缺少指导的节点: {total_nodes - nodes_with_instructions}")
        
        # 验证统计结果
        expected_total = 4  # 第一章、1.1、1.2、第二章
        expected_with_instructions = 3  # 第一章、1.1、第二章（1.2缺少）
        
        if total_nodes == expected_total:
            print("   ✅ 总节点数统计正确")
        else:
            print(f"   ❌ 总节点数统计错误: 期望{expected_total}, 实际{total_nodes}")
            return False
        
        if nodes_with_instructions == expected_with_instructions:
            print("   ✅ 有指导节点数统计正确")
        else:
            print(f"   ❌ 有指导节点数统计错误: 期望{expected_with_instructions}, 实际{nodes_with_instructions}")
            return False
        
        print("🎉 流程控制逻辑测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 流程控制测试失败: {str(e)}")
        return False

def test_task_instruction_validation():
    """测试任务指导验证逻辑"""
    print("\n🧪 测试任务指导验证逻辑")
    print("=" * 50)
    
    try:
        from complete_report_generator import CompleteReportGenerator
        
        # 创建生成器
        generator = CompleteReportGenerator(use_async=False)
        
        # 测试完整的任务指导
        complete_framework = {
            "sections": [
                {
                    "title": "第一章",
                    "level": 1,
                    "task_instruction": {"content_requirements": "分析", "word_count": "1000字"},
                    "children": [
                        {
                            "title": "1.1 子章节",
                            "level": 2,
                            "task_instruction": {"content_requirements": "详细分析", "word_count": "500字"},
                            "children": []
                        }
                    ]
                }
            ]
        }
        
        total = generator._count_all_nodes(complete_framework["sections"])
        with_instructions = generator._count_nodes_with_instructions(complete_framework["sections"])
        
        print(f"   完整框架 - 总节点: {total}, 有指导: {with_instructions}")
        
        if total == with_instructions:
            print("   ✅ 完整任务指导验证通过")
        else:
            print("   ❌ 完整任务指导验证失败")
            return False
        
        # 测试不完整的任务指导
        incomplete_framework = {
            "sections": [
                {
                    "title": "第一章",
                    "level": 1,
                    "task_instruction": {"content_requirements": "分析", "word_count": "1000字"},
                    "children": [
                        {
                            "title": "1.1 子章节",
                            "level": 2,
                            # 缺少task_instruction
                            "children": []
                        }
                    ]
                }
            ]
        }
        
        total = generator._count_all_nodes(incomplete_framework["sections"])
        with_instructions = generator._count_nodes_with_instructions(incomplete_framework["sections"])
        
        print(f"   不完整框架 - 总节点: {total}, 有指导: {with_instructions}")
        
        if total > with_instructions:
            print("   ✅ 不完整任务指导检测通过")
        else:
            print("   ❌ 不完整任务指导检测失败")
            return False
        
        print("🎉 任务指导验证逻辑测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 任务指导验证测试失败: {str(e)}")
        return False

def test_error_handling():
    """测试错误处理逻辑"""
    print("\n🧪 测试错误处理逻辑")
    print("=" * 50)
    
    try:
        from complete_report_generator import CompleteReportGenerator
        
        # 创建生成器
        generator = CompleteReportGenerator(use_async=False)
        
        # 测试空框架
        empty_framework = {"sections": []}
        
        total = generator._count_all_nodes(empty_framework["sections"])
        with_instructions = generator._count_nodes_with_instructions(empty_framework["sections"])
        
        print(f"   空框架 - 总节点: {total}, 有指导: {with_instructions}")
        
        if total == 0 and with_instructions == 0:
            print("   ✅ 空框架处理正确")
        else:
            print("   ❌ 空框架处理错误")
            return False
        
        # 测试异常框架结构
        malformed_framework = {
            "sections": [
                {
                    "title": "测试章节",
                    "level": 1,
                    # 没有children字段
                }
            ]
        }
        
        total = generator._count_all_nodes(malformed_framework["sections"])
        with_instructions = generator._count_nodes_with_instructions(malformed_framework["sections"])
        
        print(f"   异常框架 - 总节点: {total}, 有指导: {with_instructions}")
        
        if total == 1 and with_instructions == 0:
            print("   ✅ 异常框架处理正确")
        else:
            print("   ❌ 异常框架处理错误")
            return False
        
        print("🎉 错误处理逻辑测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 错误处理测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试流程控制修复效果")
    print("=" * 60)
    print("修复内容:")
    print("1. 修复任务指导生成的缩进错误")
    print("2. 添加任务指导完成验证")
    print("3. 增强错误处理和流程控制")
    print("4. 确保严格按照1→2→3→4→5→6→7的顺序执行")
    print("=" * 60)
    
    # 测试结果
    results = []
    
    # 1. 测试流程控制逻辑
    results.append(("流程控制逻辑", test_flow_control_logic()))
    
    # 2. 测试任务指导验证
    results.append(("任务指导验证", test_task_instruction_validation()))
    
    # 3. 测试错误处理
    results.append(("错误处理逻辑", test_error_handling()))
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！流程控制修复成功")
        print("\n💡 修复效果:")
        print("✅ 任务指导生成缩进错误已修复")
        print("✅ 添加了任务指导完成验证")
        print("✅ 增强了错误处理机制")
        print("✅ 确保严格按照正确顺序执行")
        print("\n📋 正确的执行流程:")
        print("1. 读取框架文件作为参考，结合主题生成框架（所有标题）")
        print("2. 待框架生成完成后，为每个框架生成任务指导")
        print("3. 待任务指导生成完成后，进入执行模型并行生成具体内容")
        print("4. 待内容生成完成后，进行第一轮检查优化")
        print("5. 待第一轮优化完成后，进行第二轮优化")
        print("6. 待第二轮优化完成后，进行第三轮优化")
        print("7. 待第三轮优化完成后，进行第四轮优化")
        return True
    else:
        print("⚠️ 部分测试失败，需要进一步修复")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
