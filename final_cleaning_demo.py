#!/usr/bin/env python3
"""
最终清理功能演示
展示如何生成纯净的报告文档，同时保留调试信息
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from complete_report_generator import CompleteReportGenerator

async def demonstrate_final_cleaning():
    """演示最终清理功能"""
    print("🎯 最终清理功能演示")
    print("="*60)
    
    # 创建生成器
    generator = CompleteReportGenerator(use_async=True)
    
    # 模拟用户输入字数限制
    target_words = 5000  # 假设用户希望生成5000字的报告
    generator.report_config["target_words"] = target_words
    
    print(f"📊 用户设定目标字数: {target_words:,} 字")
    print()
    
    # 创建包含问题内容的模拟框架（模拟生成过程的结果）
    mock_framework = {
        "title": "人工智能发展趋势报告",
        "sections": [
            {
                "title": "第一章 概述",
                "content": """
# 人工智能发展概述

人工智能技术正在快速发展，对各行各业产生深远影响。

优化后的内容严格遵循了参考报告的专业标准和风格，具体体现在：

1. **重构章节结构**：为了逻辑更清晰、内容更完整，将原内容拆分为四个子章节：`1.1 技术背景`、`1.2 发展现状`、`1.3 应用领域`，并新增`1.4 未来趋势`。这种结构使各部分各司其职，层次分明。

2. **强化问题导入（解决"深度分析"问题）**：在`1.1`中，紧随AI价值论述之后，精准切入其发展瓶颈，从而凸显技术突破的"破局意义"，增强报告的"当下必要性"。

**内容结构**：采用"总-分"结构，首先给出核心定义，然后分点阐述其具体内涵，逻辑清晰。

**写作风格**：语言客观、精炼，结论前置。通过引入行业数据和权威机构名称来增强专业性。

**格式规范**：使用了层级分明的标题，极大提升了信息传递效率和专业感。

### **优化后的完整章节内容**

## 1.1 技术背景

人工智能作为新一代信息技术的核心，正在推动第四次工业革命。根据IDC数据，全球AI市场规模预计将在2025年达到1900亿美元。

## 1.2 发展现状

当前AI技术在机器学习、深度学习、自然语言处理等领域取得重大突破。

优化后的章节内容更加专业和完整。
""" * 3,  # 重复3次增加字数
                "children": [
                    {
                        "title": "1.1 技术背景",
                        "content": "AI技术背景分析内容..." * 200,
                        "children": []
                    },
                    {
                        "title": "1.2 发展现状", 
                        "content": "AI发展现状分析内容..." * 200,
                        "children": []
                    }
                ]
            },
            {
                "title": "第二章 应用分析",
                "content": """
# AI应用分析

人工智能在各个领域的应用日益广泛。

具体体现在：

1. **医疗健康**：AI在医疗诊断、药物研发等方面发挥重要作用。
2. **金融服务**：智能风控、算法交易等应用快速发展。
3. **智能制造**：工业4.0推动制造业智能化转型。

**专业标准**：精准运用了"机器学习"、"深度学习"、"神经网络"等专业术语，体现了分析深度。

## 2.1 医疗健康应用

AI在医疗领域的应用前景广阔，包括影像诊断、精准医疗等。

优化后的内容更加专业。
""" * 2,  # 重复2次
                "children": []
            }
        ]
    }
    
    print("📝 模拟生成过程完成，包含以下问题:")
    print("   • 大量的思考过程和优化说明")
    print("   • 超出目标字数的内容")
    print("   • AI分析过程的元数据")
    print()
    
    # 统计原始字数
    original_words = generator._count_framework_words(mock_framework)
    print(f"📊 原始总字数: {original_words:,} 字")
    print(f"🎯 目标字数: {target_words:,} 字")
    print(f"📈 超出字数: {original_words - target_words:,} 字")
    print()
    
    # 展示原始内容示例
    print("📄 原始内容示例（包含问题）:")
    sample_content = mock_framework["sections"][0]["content"][:500]
    print(sample_content + "...")
    print()
    
    print("🧹 开始执行最终清理流程...")
    print("-" * 40)
    
    # 执行完整的清理流程（模拟_generate_word_document中的步骤）
    
    # 第一步：深拷贝框架，避免修改原始数据
    import copy
    cleaned_framework = copy.deepcopy(mock_framework)
    
    # 第二步：清理框架内容，移除思考过程
    print("🧹 步骤1: 清理思考过程和优化说明...")
    cleaned_framework = generator._clean_framework_for_final_output(cleaned_framework)
    
    # 第三步：强制字数控制
    print("📏 步骤2: 执行字数控制...")
    generator._enforce_word_limit_on_framework(cleaned_framework, target_words)
    
    # 第四步：验证最终结果
    final_words = generator._count_framework_words(cleaned_framework)
    print(f"✅ 步骤3: 验证最终字数: {final_words:,} 字")
    print()
    
    # 展示清理后的内容
    print("📄 清理后的内容示例（纯净版本）:")
    cleaned_sample = cleaned_framework["sections"][0]["content"][:500]
    print(cleaned_sample + "...")
    print()
    
    # 验证清理效果
    print("🔍 清理效果验证:")
    
    # 检查是否还有问题短语
    all_content = ""
    def collect_content(sections):
        nonlocal all_content
        for section in sections:
            all_content += section.get("content", "")
            if "children" in section:
                collect_content(section["children"])
    
    collect_content(cleaned_framework["sections"])
    
    problem_phrases = [
        "优化后的内容严格遵循",
        "具体体现在：",
        "**重构章节结构**：",
        "**强化问题导入",
        "**内容结构**：",
        "**写作风格**：",
        "**格式规范**：",
        "**专业标准**：",
        "### **优化后的",
        "优化后的章节内容"
    ]
    
    remaining_issues = [phrase for phrase in problem_phrases if phrase in all_content]
    
    if remaining_issues:
        print("❌ 仍存在问题短语:")
        for issue in remaining_issues:
            print(f"   - {issue}")
    else:
        print("✅ 所有问题短语已清理")
    
    # 字数控制验证
    if final_words <= target_words:
        print(f"✅ 字数控制成功: {final_words:,} ≤ {target_words:,}")
    else:
        print(f"❌ 字数控制失败: {final_words:,} > {target_words:,}")
    
    print()
    print("📋 功能特点总结:")
    print("✅ 保留原始框架用于调试（包含所有分析过程）")
    print("✅ 生成纯净的最终文档（移除思考过程）")
    print("✅ 严格控制字数在用户指定范围内")
    print("✅ 保持报告的专业性和可读性")
    
    return cleaned_framework

async def demonstrate_usage_workflow():
    """演示完整的使用流程"""
    print("\n" + "="*60)
    print("📚 完整使用流程演示")
    print("="*60)
    
    print("💡 实际使用时的完整流程：")
    print("""
1. 用户启动程序，输入目标字数（例如：50000字）
2. 系统执行完整的报告生成流程：
   - 框架生成（保留所有分析过程）
   - 内容生成（保留所有思考过程）
   - 迭代优化（保留所有优化记录）
3. 在最终文档生成阶段：
   - 自动清理所有思考过程和优化说明
   - 强制执行用户指定的字数限制
   - 生成纯净的Word和Markdown文档
4. 结果：
   - 中间过程文件：包含完整分析，用于调试
   - 最终报告文档：纯净内容，符合字数要求
""")
    
    print("🔧 代码示例：")
    print("""
# 创建生成器并设置目标字数
generator = CompleteReportGenerator(use_async=True)
generator.report_config["target_words"] = 30000  # 用户输入的字数

# 异步生成报告（包含所有优化功能）
output_path = await generator.generate_report_async(
    topic="您的报告主题",
    data_sources=["数据源1.pdf", "数据源2.xlsx"],
    framework_file_path="框架文件.txt"
)

# 结果：
# - 中间文件保存在checkpoints/目录（用于调试）
# - 最终文档保存在output/目录（纯净版本）
""")

def main():
    """主函数"""
    print("🎯 AI报告生成器 - 最终清理功能演示")
    print("解决问题：")
    print("1. 移除AI的思考过程和优化说明")
    print("2. 强制执行用户指定的字数限制")
    print("3. 保留调试信息的同时生成纯净文档")
    print()
    
    # 运行演示
    asyncio.run(demonstrate_final_cleaning())
    asyncio.run(demonstrate_usage_workflow())
    
    print("\n🎉 演示完成！")
    print("📚 更多信息请查看 test_content_cleaning.py")

if __name__ == "__main__":
    main()
