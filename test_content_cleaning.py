#!/usr/bin/env python3
"""
测试内容清理功能
验证是否能正确移除思考过程和优化说明
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from complete_report_generator import CompleteReportGenerator

def test_thinking_process_removal():
    """测试思考过程移除功能"""
    print("🧪 测试思考过程移除功能...")
    
    # 创建生成器实例
    generator = CompleteReportGenerator()
    
    # 测试内容（包含您提到的具体问题 - 增强版）
    test_content = """
# 地热能发展现状

地热能作为清洁可再生能源，具有重要的战略价值。

原有的技术介绍内容将被剥离，并构建一个以市场数据和战略分析为核心的全新框架。

以下是优化后的完整章节内容：

优化后的内容严格遵循了参考报告的专业标准和风格，具体体现在：

1. **重构章节结构**：为了逻辑更清晰、内容更完整，将原内容拆分为四个子章节：`1.1 战略背景与核心价值`、`1.2 研究框架与核心议题`、`1.3 研究范围、分析逻辑与数据基准`，并新增`1.4 核心概念界定与技术分类`。这种结构使引言、研究问题、研究范围和基础定义各司其职，层次分明。

2. **强化问题导入（解决"深度分析"问题）**：在`1.1`中，紧随地热能价值论述之后，精准切入其传统发展瓶颈，从而凸显以EGS为代表的技术突破的"破局意义"，增强报告的"当下必要性"，并自然引出后续章节的重点。

优化后的版本在结构上采用了"总-分"模式，将核心观点前置；在风格上强化了数据驱动和客观陈述；在格式上使用了多级标题和图表引用；在专业性上则力求分析更深入、术语更精准。

{ "sections": [ { "title": "概述与背景", "level": 1, "children": [ {"title": "研究背景", "level": 2}, {"title": "研究目的", "level": 2}, {"title": "研究方法", "level": 2} ] }, { "title": "现状分析", "level": 1, "children": [ {"title": "发展现状", "level": 2}, {"title": "主要特征", "level": 2}, {"title": "存在问题", "level": 2} ] } ] }

### **优化后的完整章节内容**

## 1.1 战略背景与核心价值

地热能作为地球内部热能的直接利用形式，具有储量巨大、分布广泛、清洁环保等显著优势。

**内容结构**：采用"总-分"结构，首先给出核心定义，然后分点阐述其具体内涵、分类体系和战略地位，逻辑清晰。

**写作风格**：语言客观、精炼，结论前置。通过引入行业数据（如利用小时数）和权威机构名称（如IEA）来增强专业性，并使用了前瞻性表述。

**格式规范**：使用了层级分明的标题，并创建了一个关键的"图表"来可视化核心信息，极大提升了信息传递效率和专业感。

**专业标准**：精准运用了"基荷电源"、"地温梯度"、"热储"、"干热岩（EGS）"等行业术语，并从能源结构转型的战略高度分析了地热能的价值，体现了分析深度。

## 1.2 技术发展趋势

当前地热能技术正朝着更高效、更经济的方向发展。

优化后的章节内容更加专业和完整。
"""
    
    print("📝 原始内容长度:", len(test_content))
    print("📝 原始内容预览:")
    print(test_content[:200] + "...")
    print()
    
    # 执行清理
    cleaned_content = generator._remove_thinking_process(test_content)
    
    print("🧹 清理后内容长度:", len(cleaned_content))
    print("🧹 清理后内容:")
    print(cleaned_content)
    print()
    
    # 验证清理效果（增强版）
    problematic_phrases = [
        "优化后的内容严格遵循",
        "具体体现在：",
        "**重构章节结构**：",
        "**强化问题导入",
        "**明确数据基准",
        "**补充核心定义",
        "**内容结构**：",
        "**写作风格**：",
        "**格式规范**：",
        "**专业标准**：",
        "### **优化后的",
        "优化后的章节内容",
        # 新发现的问题模式
        "原有的",
        "内容将被剥离",
        "以下是优化后的完整章节内容：",
        "优化后的版本在结构上采用了",
        "在风格上强化了",
        "在专业性上则",
        "并构建一个以",
        "为核心的全新框架",
        '"sections":',
        '{ "title":',
        '"level": 1',
        '"level": 2',
        '"children": [',
        "采用了\"总-分\"模式",
        "将核心观点前置",
        "力求分析更深入"
    ]
    
    remaining_issues = []
    for phrase in problematic_phrases:
        if phrase in cleaned_content:
            remaining_issues.append(phrase)
    
    if remaining_issues:
        print("❌ 仍存在的问题短语:")
        for issue in remaining_issues:
            print(f"   - {issue}")
        return False
    else:
        print("✅ 所有问题短语已清理完毕")
        return True

def test_word_count_control():
    """测试字数控制功能"""
    print("\n🧪 测试字数控制功能...")
    
    generator = CompleteReportGenerator()
    
    # 创建测试框架
    test_framework = {
        "sections": [
            {
                "title": "第一章 概述",
                "content": "这是一个很长的内容" * 1000,  # 约15000字
                "children": [
                    {
                        "title": "1.1 背景",
                        "content": "这是背景内容" * 500,  # 约3500字
                        "children": []
                    }
                ]
            },
            {
                "title": "第二章 分析",
                "content": "这是分析内容" * 800,  # 约5600字
                "children": []
            }
        ]
    }
    
    # 统计原始字数
    original_words = generator._count_framework_words(test_framework)
    print(f"📊 原始字数: {original_words:,} 字")
    
    # 设置目标字数
    target_words = 10000
    print(f"🎯 目标字数: {target_words:,} 字")
    
    # 执行字数控制
    generator._enforce_word_limit_on_framework(test_framework, target_words)
    
    # 统计最终字数
    final_words = generator._count_framework_words(test_framework)
    print(f"✅ 最终字数: {final_words:,} 字")
    
    # 验证是否达到目标
    if final_words <= target_words:
        print("✅ 字数控制成功")
        return True
    else:
        print(f"❌ 字数控制失败，超出目标 {final_words - target_words:,} 字")
        return False

def test_complete_cleaning():
    """测试完整的清理流程"""
    print("\n🧪 测试完整的清理流程...")
    
    generator = CompleteReportGenerator()
    
    # 创建包含问题内容的测试框架
    test_framework = {
        "sections": [
            {
                "title": "测试章节",
                "content": """
# 地热能技术分析

地热能是重要的可再生能源。

优化后的内容严格遵循了参考报告的专业标准和风格，具体体现在：

1. **重构章节结构**：为了逻辑更清晰、内容更完整，将原内容拆分为多个子章节。这种结构使各部分各司其职，层次分明。

2. **强化问题导入（解决"深度分析"问题）**：在章节中，紧随价值论述之后，精准切入发展瓶颈。

**内容结构**：采用"总-分"结构，逻辑清晰。

**写作风格**：语言客观、精炼，结论前置。

## 技术现状

当前地热能技术发展迅速，具有良好的应用前景。

优化后的章节内容更加专业和完整。
""",
                "children": []
            }
        ]
    }
    
    print("📝 清理前内容:")
    print(test_framework["sections"][0]["content"][:300] + "...")
    
    # 执行完整清理
    cleaned_framework = generator._clean_framework_for_final_output(test_framework)
    
    print("\n🧹 清理后内容:")
    print(cleaned_framework["sections"][0]["content"])
    
    # 检查是否还有问题内容
    cleaned_content = cleaned_framework["sections"][0]["content"]
    has_issues = any(phrase in cleaned_content for phrase in [
        "优化后的内容严格遵循",
        "具体体现在：",
        "**重构章节结构**：",
        "**内容结构**：",
        "**写作风格**：",
        "优化后的章节内容"
    ])
    
    if has_issues:
        print("❌ 完整清理流程存在问题")
        return False
    else:
        print("✅ 完整清理流程成功")
        return True

def test_image_content_integration():
    """测试图片内容整合功能"""
    print("\n🧪 测试图片内容整合功能...")

    generator = CompleteReportGenerator()

    # 创建模拟的图片关联数据
    import json
    from pathlib import Path

    # 确保correlation_cache目录存在
    cache_dir = Path("correlation_cache")
    cache_dir.mkdir(exist_ok=True)

    # 创建模拟的关联数据
    mock_correlation_data = {
        "pdf_file": "test_report.pdf",
        "total_images": 2,
        "correlations": [
            {
                "page_num": 1,
                "description": "技术发展趋势图表",
                "analysis": "该图表显示了人工智能技术在过去五年的发展趋势，包括机器学习、深度学习等关键技术的进步情况。数据表明技术发展呈现加速态势。",
                "filename": "tech_trend_chart.png"
            },
            {
                "page_num": 2,
                "description": "市场规模分析图",
                "analysis": "此图展示了全球AI市场规模的增长预测，从2020年的500亿美元增长到2025年预计的1900亿美元，年复合增长率达到30%。",
                "filename": "market_size_chart.png"
            }
        ]
    }

    # 保存模拟数据
    cache_file = cache_dir / "test_correlation_data.json"
    with open(cache_file, 'w', encoding='utf-8') as f:
        json.dump(mock_correlation_data, f, ensure_ascii=False, indent=2)

    print(f"   📂 创建模拟关联数据: {cache_file}")

    # 创建测试框架
    test_framework = {
        "sections": [
            {
                "title": "技术发展分析",
                "content": "人工智能技术正在快速发展，在各个领域都有重要应用。",
                "children": []
            },
            {
                "title": "市场规模研究",
                "content": "全球AI市场呈现快速增长态势，投资规模不断扩大。",
                "children": []
            }
        ]
    }

    print("📝 原始框架:")
    for section in test_framework["sections"]:
        print(f"   - {section['title']}: {len(section['content'])} 字符")

    # 执行图片内容整合
    generator._integrate_image_content_to_framework(test_framework)

    print("\n🖼️ 整合后框架:")
    has_images = False
    for section in test_framework["sections"]:
        print(f"   - {section['title']}: {len(section['content'])} 字符")
        if "图" in section['content']:
            has_images = True
            print(f"     ✅ 包含图片内容")

    # 清理测试文件
    cache_file.unlink()

    if has_images:
        print("✅ 图片内容整合测试通过")
        return True
    else:
        print("❌ 图片内容整合测试失败")
        return False

def main():
    """主函数"""
    print("🧪 内容清理功能测试")
    print("="*50)
    
    # 运行所有测试
    test1_result = test_thinking_process_removal()
    test2_result = test_word_count_control()
    test3_result = test_complete_cleaning()
    test4_result = test_image_content_integration()

    print("\n" + "="*50)
    print("📊 测试结果总结:")
    print(f"   思考过程移除: {'✅ 通过' if test1_result else '❌ 失败'}")
    print(f"   字数控制功能: {'✅ 通过' if test2_result else '❌ 失败'}")
    print(f"   完整清理流程: {'✅ 通过' if test3_result else '❌ 失败'}")
    print(f"   图片内容整合: {'✅ 通过' if test4_result else '❌ 失败'}")

    if all([test1_result, test2_result, test3_result, test4_result]):
        print("\n🎉 所有测试通过！内容清理和图片整合功能正常工作")
        print("\n📋 功能说明:")
        print("✅ 彻底移除AI的思考过程和优化说明")
        print("✅ 强制执行用户指定的字数限制")
        print("✅ 智能整合图片内容到相应章节")
        print("✅ 保留纯净的报告正文内容")
        print("✅ 保留所有分析过程用于调试")
        return True
    else:
        print("\n❌ 部分测试失败，请检查实现")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
