#!/usr/bin/env python3
"""
测试Metaso搜索功能
验证网页搜索和学术搜索的智能切换
"""

import os
import sys
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

def test_metaso_search_basic():
    """测试基础的Metaso搜索功能"""
    print("🧪 测试基础Metaso搜索功能")
    print("=" * 50)
    
    try:
        from complete_report_generator import CompleteReportGenerator
        
        # 创建生成器
        generator = CompleteReportGenerator(use_async=False)
        search_manager = generator.SearchManager(generator)
        
        # 测试网页搜索
        print("🌐 测试网页搜索...")
        webpage_results = search_manager.search_metaso("地热发电市场规模", "webpage", 3)
        
        if webpage_results:
            print(f"   ✅ 网页搜索成功，获得 {len(webpage_results)} 个结果")
            for i, result in enumerate(webpage_results[:2]):
                print(f"      {i+1}. {result.get('title', '无标题')[:50]}...")
        else:
            print("   ❌ 网页搜索失败")
            return False
        
        # 测试学术搜索
        print("\n🎓 测试学术搜索...")
        scholar_results = search_manager.search_metaso("地热发电技术研究", "scholar", 3)
        
        if scholar_results:
            print(f"   ✅ 学术搜索成功，获得 {len(scholar_results)} 个结果")
            for i, result in enumerate(scholar_results[:2]):
                print(f"      {i+1}. {result.get('title', '无标题')[:50]}...")
        else:
            print("   ❌ 学术搜索失败")
            return False
        
        print("🎉 基础搜索功能测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 基础搜索测试失败: {str(e)}")
        return False

def test_intelligent_search_mode():
    """测试智能搜索模式选择"""
    print("\n🧪 测试智能搜索模式选择")
    print("=" * 50)
    
    try:
        from complete_report_generator import CompleteReportGenerator
        
        # 创建生成器
        generator = CompleteReportGenerator(use_async=False)
        search_manager = generator.SearchManager(generator)
        
        # 测试用例：应该使用网页搜索的查询
        webpage_queries = [
            "地热发电市场规模",
            "地热能源投资机会",
            "地热发电公司排名",
            "地热能源政策支持"
        ]
        
        # 测试用例：应该使用学术搜索的查询
        scholar_queries = [
            "地热发电技术研究",
            "地热能源学术论文",
            "地热发电实验数据",
            "地热能源科学分析"
        ]
        
        print("📊 测试网页搜索模式判断...")
        for query in webpage_queries:
            scope = search_manager._determine_search_scope(query)
            if scope == 'webpage':
                print(f"   ✅ '{query}' → {scope} (正确)")
            else:
                print(f"   ❌ '{query}' → {scope} (错误，应为webpage)")
                return False
        
        print("\n📚 测试学术搜索模式判断...")
        for query in scholar_queries:
            scope = search_manager._determine_search_scope(query)
            if scope == 'scholar':
                print(f"   ✅ '{query}' → {scope} (正确)")
            else:
                print(f"   ❌ '{query}' → {scope} (错误，应为scholar)")
                return False
        
        print("🎉 智能搜索模式选择测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 智能搜索模式测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试Metaso搜索功能")
    print("=" * 60)
    print("测试内容:")
    print("1. 基础Metaso搜索功能（网页+学术）")
    print("2. 智能搜索模式选择")
    print("=" * 60)
    
    # 测试结果
    results = []
    
    # 1. 测试基础搜索功能
    results.append(("基础Metaso搜索", test_metaso_search_basic()))
    
    # 2. 测试智能模式选择
    results.append(("智能搜索模式选择", test_intelligent_search_mode()))
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！Metaso搜索功能正常")
        print("\n💡 功能特点:")
        print("✅ 支持网页搜索和学术搜索两种模式")
        print("✅ 智能判断搜索模式（学术关键词→学术搜索，其他→网页搜索）")
        print("✅ 使用固定API密钥 mk-988A8E4DC50C53312E3D1A8729687F4C")
        print("✅ 支持主题动态替换（替换'硫化物固态电解质'为实际主题）")
        print("✅ 完整的错误处理和日志输出")
        return True
    else:
        print("⚠️ 部分测试失败，需要检查网络连接或API配置")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
