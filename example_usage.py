#!/usr/bin/env python3
"""
优化后的AI报告生成器使用示例
展示新增的功能：
1. 强制使用所有API KEY的最大并发
2. 增强的内容提取和错误处理
3. 完整的checkpoint机制
4. 详细的进度条显示
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from complete_report_generator import CompleteReportGenerator, AsyncConfig

async def demonstrate_optimizations():
    """演示优化后的功能"""
    print("🚀 AI报告生成器优化功能演示")
    print("="*60)
    
    # 1. 创建优化后的生成器
    print("1️⃣ 创建优化后的异步生成器...")
    generator = CompleteReportGenerator(use_async=True)
    
    # 显示API配置信息
    perf_info = AsyncConfig.get_performance_info()
    print(f"   📊 API配置:")
    print(f"      可用API密钥: {perf_info['available_api_keys']} 个")
    print(f"      最大并发数: {perf_info['max_concurrent_requests']}")
    print(f"      预计加速: {perf_info['estimated_speedup']}")
    print()
    
    # 2. 演示API KEY管理优化
    print("2️⃣ 演示API KEY管理优化...")
    
    # 强制使用最大并发数
    max_concurrent = AsyncConfig.get_dynamic_concurrent_requests(generator.api_manager)
    available_keys = AsyncConfig.get_available_api_count()
    print(f"   🔧 强制并发数: {max_concurrent} (等于API数量: {available_keys})")
    
    # 显示API状态
    api_status = generator.api_manager.get_api_status_summary()
    print(f"   📈 API状态: 可用={api_status['available']}, 总数={api_status['total']}")
    print()
    
    # 3. 演示checkpoint功能
    print("3️⃣ 演示checkpoint功能...")
    
    # 创建测试checkpoint
    test_data = {
        "demo_stage": "演示阶段",
        "timestamp": "2025-07-31",
        "features": ["API优化", "内容提取", "进度条", "checkpoint"]
    }
    
    checkpoint_id = generator.create_checkpoint("demo_optimization", test_data)
    print(f"   💾 创建checkpoint: {checkpoint_id}")
    
    # 列出所有checkpoint
    checkpoints = generator.list_checkpoints()
    print(f"   📂 当前checkpoint数量: {len(checkpoints)}")
    
    # 加载checkpoint
    loaded_data = generator.load_checkpoint(checkpoint_id)
    print(f"   📥 加载checkpoint成功: {loaded_data.get('demo_stage', '未知')}")
    print()
    
    # 4. 演示内容提取优化
    print("4️⃣ 演示内容提取优化...")
    
    # 测试空内容处理
    class MockResponse:
        def __init__(self, content=""):
            self.text = content
            self.candidates = []
    
    # 正常内容
    normal_response = MockResponse("这是正常的AI生成内容")
    normal_content = generator._extract_content(normal_response)
    print(f"   ✅ 正常内容提取: {len(normal_content)} 字符")
    
    # 空内容（触发备用机制）
    empty_response = MockResponse("")
    backup_content = generator._extract_content(empty_response)
    print(f"   🔄 备用内容生成: {len(backup_content)} 字符")
    print(f"   📝 备用内容预览: {backup_content[:50]}...")
    print()
    
    # 5. 演示进度条功能
    print("5️⃣ 演示进度条功能...")
    
    try:
        from tqdm.asyncio import tqdm
        
        # 模拟报告生成过程的进度条
        steps = ["框架生成", "任务指导", "内容生成", "迭代优化", "文档生成"]
        
        print("   📊 模拟报告生成进度:")
        pbar = tqdm(total=len(steps), desc="报告生成", unit="步骤")
        
        for i, step in enumerate(steps):
            await asyncio.sleep(0.5)  # 模拟工作时间
            print(f"      ✅ 完成: {step}")
            pbar.update(1)
        
        pbar.close()
        print("   🎉 进度条演示完成")
        
    except ImportError:
        print("   ⚠️ tqdm未安装，无法演示进度条")
        print("   💡 安装命令: pip install tqdm")
    
    print()
    
    # 6. 总结优化效果
    print("6️⃣ 优化效果总结...")
    print("   ✅ API利用率最大化：强制使用所有可用API KEY")
    print("   ✅ 内容质量保障：智能备用内容生成机制")
    print("   ✅ 进度可视化：全面的tqdm进度条显示")
    print("   ✅ 错误恢复：完善的checkpoint断点续传")
    print("   ✅ 系统稳定性：增强的错误处理和重试机制")
    print()
    
    print("🎯 优化完成！系统已准备用于生产环境")

async def example_report_generation():
    """示例：生成一个简单的报告（演示用）"""
    print("\n" + "="*60)
    print("📄 报告生成示例（演示模式）")
    print("="*60)
    
    # 注意：这里只是演示，不会实际调用API
    print("💡 实际使用时的代码示例：")
    print("""
# 创建异步生成器
generator = CompleteReportGenerator(use_async=True)

# 异步生成报告（包含所有优化功能）
output_path = await generator.generate_report_async(
    topic="人工智能发展趋势报告",
    data_sources=["data/ai_research.pdf", "data/market_data.xlsx"],
    framework_file_path="frameworks/ai_report_framework.txt"
)

print(f"报告生成完成: {output_path}")
""")
    
    print("🔧 优化功能说明：")
    print("   • 自动使用所有10个API KEY进行最大并发")
    print("   • 实时显示详细的进度条")
    print("   • 每个步骤自动保存checkpoint")
    print("   • 智能错误处理和内容备用机制")
    print("   • 支持断点续传和错误恢复")

def main():
    """主函数"""
    print("AI报告生成器优化功能演示")
    print("本演示将展示所有新增的优化功能")
    print()
    
    # 运行演示
    asyncio.run(demonstrate_optimizations())
    
    # 显示使用示例
    asyncio.run(example_report_generation())
    
    print("\n🎉 演示完成！")
    print("📚 更多信息请查看 OPTIMIZATION_SUMMARY.md")

if __name__ == "__main__":
    main()
